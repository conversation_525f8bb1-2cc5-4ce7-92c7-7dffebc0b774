<script lang="ts">
    import AppLayout from '@/layouts/AppLayout.svelte';
    import { Button } from '@/components/ui/button';
    import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
    import { Badge } from '@/components/ui/badge';
    import { Alert, AlertDescription } from '@/components/ui/alert';
    import StatusBadge from '@/components/prompts/StatusBadge.svelte';
    import QueueJobsList from '@/components/prompts/QueueJobsList.svelte';
    import ProcessingTimeTag from '@/components/prompts/ProcessingTimeTag.svelte';
    import DeleteConfirmDialog from '@/components/prompts/DeleteConfirmDialog.svelte';
    import AddJobDialog from '@/components/prompts/AddJobDialog.svelte';
    import {
        Edit,
        RefreshCw,
        Copy,
        ExternalLink,
        Calendar,
        FileText,
        MessageSquare,
        Paperclip,
        AlertCircle,
        Loader2
    } from 'lucide-svelte';
    import { api } from '@/lib/api';
    import type { Prompt, BreadcrumbItem } from '@/types';
    import { onMount } from 'svelte';
    import { router } from '@inertiajs/svelte';
    import { formatDate } from '@/lib/utils/time';

    interface Props {
        uuid: string;
    }

    let { uuid }: Props = $props();

    // State
    let prompt = $state<Prompt | null>(null);
    let loading = $state(true);
    let error = $state<string | null>(null);
    let refreshing = $state(false);

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Prompts', href: '/prompts' },
        { title: `Prompt #${uuid.substring(0, 8)}`, href: `/prompts/${uuid}` }
    ];

    async function loadPrompt() {
        try {
            loading = true;
            error = null;
            prompt = await api.getPrompt(uuid);
        } catch (err) {
            error = err instanceof Error ? err.message : 'Failed to load prompt';
            console.error('Error loading prompt:', err);
        } finally {
            loading = false;
        }
    }

    async function refreshPrompt() {
        try {
            refreshing = true;
            prompt = await api.getPrompt(uuid);
        } catch (err) {
            console.error('Error refreshing prompt:', err);
        } finally {
            refreshing = false;
        }
    }

    function formatFileSize(bytes: number): string {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    const deletePrompt = async () => {
        if (!prompt) return;

        try {
            await api.deletePrompt(prompt.uuid);
            router.visit('/prompts');
        } catch (error) {
            console.error('Error deleting prompt:', error);
            // Aqui você pode adicionar uma notificação de erro
        }
    };

    const deleteResponse = async (responseUuid: string) => {
        if (!prompt) return;

        try {
            await api.deleteResponse(prompt.uuid, responseUuid);
            // Recarregar o prompt para atualizar a lista de respostas
            await loadPrompt();
        } catch (error) {
            console.error('Error deleting response:', error);
            // Aqui você pode adicionar uma notificação de erro
        }
    };

    const handleJobCreated = async () => {
        // Recarregar o prompt para atualizar as respostas
        await loadPrompt();
    };

    // Obter modelos que já têm respostas
    const existingModels = $derived(
        prompt?.responses?.map(response => response.model) || []
    );

    async function copyToClipboard(text: string) {
        try {
            await navigator.clipboard.writeText(text);
            // You could add a toast notification here
        } catch (err) {
            console.error('Failed to copy to clipboard:', err);
        }
    }

    onMount(() => {
        loadPrompt();
        
        // Auto-refresh if prompt is still processing
        const interval = setInterval(() => {
            if (prompt && (prompt.status === 'pending' || prompt.status === 'processing')) {
                refreshPrompt();
            } else {
                clearInterval(interval);
            }
        }, 5000);

        return () => clearInterval(interval);
    });
</script>

<svelte:head>
    <title>Prompt #{uuid.substring(0, 8)} - QueueAI</title>
</svelte:head>

<AppLayout {breadcrumbs}>
    <div class="max-w-6xl mx-auto space-y-6 p-6">
        {#if loading}
            <div class="flex items-center justify-center py-12">
                <Loader2 class="h-6 w-6 animate-spin mr-2" />
                <span>Loading prompt...</span>
            </div>
        {:else if error}
            <Alert variant="destructive">
                <AlertCircle class="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
            </Alert>
            <div class="flex justify-center">
                <Button variant="outline" onclick={loadPrompt}>
                    <RefreshCw class="mr-2 h-4 w-4" />
                    Try Again
                </Button>
            </div>
        {:else if prompt}
            <!-- Header -->
            <div class="flex items-start justify-between">
                <div>
                    <h1 class="text-2xl font-semibold tracking-tight">
                        Prompt #{prompt.uuid.substring(0, 8)}
                    </h1>
                    <div class="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                        <div class="flex items-center gap-1">
                            <Calendar class="h-4 w-4" />
                            Created {formatDate(prompt.created_at)}
                        </div>
                        {#if prompt.processed_at}
                            <div class="flex items-center gap-1">
                                <Calendar class="h-4 w-4" />
                                Processed {formatDate(prompt.processed_at)}
                            </div>
                        {/if}
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <StatusBadge status={prompt.status} />
                    <Button variant="outline" size="sm" onclick={refreshPrompt} disabled={refreshing}>
                        <RefreshCw class="h-4 w-4 {refreshing ? 'animate-spin' : ''}" />
                    </Button>
                    <Button variant="outline" size="sm" href={`/prompts/${prompt.uuid}/edit`}>
                        <Edit class="mr-2 h-4 w-4" />
                        Edit
                    </Button>
                    <DeleteConfirmDialog
                        title="Deletar Prompt"
                        description="Tem certeza que deseja deletar este prompt? Esta ação também deletará todas as respostas relacionadas e não pode ser desfeita."
                        onConfirm={deletePrompt}
                        variant="destructive"
                        size="sm"
                        triggerText="Delete"
                    />
                </div>
            </div>

            <div class="grid gap-6 lg:grid-cols-3">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Prompt Content -->
                    <Card>
                        <CardHeader>
                            <div class="flex items-center justify-between">
                                <CardTitle class="flex items-center gap-2">
                                    <FileText class="h-5 w-5" />
                                    Prompt Content
                                </CardTitle>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onclick={() => copyToClipboard(prompt!.content)}
                                >
                                    <Copy class="h-4 w-4" />
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div class="prose prose-sm max-w-none dark:prose-invert">
                                <pre class="whitespace-pre-wrap text-sm">{prompt.content}</pre>
                            </div>
                        </CardContent>
                    </Card>

                    <!-- Queue Jobs -->
                    <QueueJobsList />

                    <!-- Responses -->
                    <Card>
                        <CardHeader>
                            <CardTitle class="flex items-center gap-2">
                                <MessageSquare class="h-5 w-5" />
                                Responses
                                {#if prompt.responses}
                                    <Badge variant="secondary">{prompt.responses.length}</Badge>
                                {/if}
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {#if prompt.responses && prompt.responses.length > 0}
                                <div class="space-y-4">
                                    {#each prompt.responses as response}
                                        <div class="border rounded-lg p-4">
                                            <div class="flex items-center justify-between mb-3">
                                                <div class="flex items-center gap-2">
                                                    <Badge variant="outline">{response.model}</Badge>
                                                    <StatusBadge status={response.status} />
                                                    <ProcessingTimeTag {response} />
                                                </div>
                                                <div class="flex items-center gap-2">
                                                    {#if response.generated_at}
                                                        <span class="text-xs text-muted-foreground">
                                                            {formatDate(response.generated_at)}
                                                        </span>
                                                    {/if}
                                                    <DeleteConfirmDialog
                                                        title="Deletar Resposta"
                                                        description="Tem certeza que deseja deletar esta resposta? Esta ação não pode ser desfeita."
                                                        onConfirm={() => deleteResponse(response.uuid)}
                                                        variant="ghost"
                                                        size="icon"
                                                        showIcon={true}
                                                    />
                                                </div>
                                            </div>
                                            
                                            {#if response.content}
                                                <div class="prose prose-sm max-w-none dark:prose-invert">
                                                    <pre class="whitespace-pre-wrap text-sm">{response.content}</pre>
                                                </div>
                                                
                                                <div class="flex justify-end mt-3">
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onclick={() => copyToClipboard(response.content)}
                                                    >
                                                        <Copy class="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            {/if}
                                        </div>
                                    {/each}
                                </div>
                            {:else}
                                <p class="text-muted-foreground text-center py-8">
                                    No responses yet. 
                                    {#if prompt.status === 'pending' || prompt.status === 'processing'}
                                        Processing in progress...
                                    {/if}
                                </p>
                            {/if}
                        </CardContent>
                    </Card>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Attachments -->
                    {#if prompt.attachments && prompt.attachments.length > 0}
                        <Card>
                            <CardHeader>
                                <CardTitle class="flex items-center gap-2">
                                    <Paperclip class="h-5 w-5" />
                                    Attachments
                                    <Badge variant="secondary">{prompt.attachments.length}</Badge>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div class="space-y-2">
                                    {#each prompt.attachments as attachment}
                                        <div class="flex items-center justify-between p-2 border rounded">
                                            <div class="flex-1 min-w-0">
                                                {#if attachment.mime_type.startsWith('image/')}
                                                    <img
                                                        src={attachment.url}
                                                        alt={attachment.name}
                                                        class="h-24 w-24 rounded object-cover mr-2"
                                                    />
                                                {/if}
                                                <p class="text-sm font-medium truncate">{attachment.name}</p>
                                                <p class="text-xs text-muted-foreground">
                                                    {formatFileSize(attachment.size)}
                                                </p>
                                            </div>
                                            <Button variant="ghost" size="sm" href={attachment.url}>
                                                <ExternalLink class="h-4 w-4" />
                                            </Button>
                                        </div>
                                    {/each}
                                </div>
                            </CardContent>
                        </Card>
                    {/if}

                    <!-- Metadata -->
                    {#if prompt.metadata && Object.keys(prompt.metadata).length > 0}
                        <Card>
                            <CardHeader>
                                <CardTitle>Metadata</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div class="space-y-2">
                                    {#each Object.entries(prompt.metadata) as [key, value]}
                                        <div>
                                            <dt class="text-sm font-medium">{key}</dt>
                                            <dd class="text-sm text-muted-foreground">{JSON.stringify(value)}</dd>
                                        </div>
                                    {/each}
                                </div>
                            </CardContent>
                        </Card>
                    {/if}
                </div>
            </div>
        {/if}
    </div>
</AppLayout>
