import '@inertiajs/svelte';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavItem {
    title: string;
    href: string;
    icon?: any;
    isActive?: boolean;
}

export type PageProps<T extends Record<string, unknown> = Record<string, unknown>> = T & {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    [key: string]: unknown;
    ziggy: Config & { location: string };
};

export interface User {
    id: number;
    name: string;
    email: string;
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
}

export type BreadcrumbItemType = BreadcrumbItem;

// Prompt Management Types
export interface Prompt {
    id: number;
    uuid: string;
    content: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    metadata: Record<string, any>;
    user_id: number;
    processed_at: string | null;
    created_at: string;
    updated_at: string;
    responses?: Response[];
    attachments?: Attachment[];
    subjects?: Subject[];
}

export interface Response {
    id: number;
    uuid: string;
    prompt_id: number;
    model: string;
    content: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    metadata: {
        processing_time?: number;
        service_type?: string;
        has_attachments?: boolean;
        attachment_types?: string[];
        [key: string]: any;
    };
    generated_at: string | null;
    created_at: string;
    updated_at: string;
}

export interface Attachment {
    id: number;
    name: string;
    file_name: string;
    mime_type: string;
    size: number;
    url: string;
}

export interface Subject {
    id: number;
    name: string;
    description?: string;
    parent_id?: number;
    created_at: string;
    updated_at: string;
}

export interface QueueJob {
    id: number;
    prompt_uuid: string | null;
    model: string | null;
    created_at: string;
    attempts: number;
    queue: string;
}

export interface CreatePromptRequest {
    content: string;
    models?: string[];
    attachments?: File[];
    metadata?: Record<string, any>;
}

export interface PromptFilters {
    search?: string;
    status?: string;
    model?: string;
    date_from?: string;
    date_to?: string;
}

export interface PaginatedResponse<T> {
    data: T[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    errors?: Record<string, string[]>;
}
