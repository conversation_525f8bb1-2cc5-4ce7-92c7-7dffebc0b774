{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix", "shadcn:update": "npx shadcn-svelte update --all"}, "devDependencies": {"@eslint/js": "^9.29.0", "@inertiajs/svelte": "^2.0.13", "@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.523.0", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@tailwindcss/vite": "^4.1.10", "@tanstack/table-core": "^8.21.3", "@tsconfig/svelte": "^5.0.4", "@types/node": "^24.0.4", "axios": "^1.10.0", "bits-ui": "^2.8.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.2.0", "embla-carousel-svelte": "^8.6.0", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.9.3", "formsnap": "^2.0.1", "globals": "^16.2.0", "laravel-vite-plugin": "^1.3.0", "layerchart": "^2.0.0-next.10", "lucide-svelte": "^0.523.0", "maildev": "^2.2.1", "mode-watcher": "^1.0.8", "paneforge": "^1.0.0", "prettier": "^3.6.1", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.13", "svelte": "^5.34.8", "svelte-check": "^4.2.2", "svelte-portal": "^2.2.1", "svelte-sonner": "^1.0.5", "svelte-transition": "^0.0.17", "sveltekit-superforms": "^2.27.0", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.10", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0", "vaul-svelte": "^1.0.0-next.7", "vite": "^6.3.5", "zod": "^3.25.67"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.44.1", "@tailwindcss/oxide-linux-x64-gnu": "^4.1.10", "lightningcss-linux-x64-gnu": "^1.30.1"}}