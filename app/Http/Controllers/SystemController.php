<?php

namespace App\Http\Controllers;

use App\Services\AI\AIService;
use App\Services\AI\OllamaService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class SystemController extends Controller
{
    /**
     * Verificar status do sistema
     */
    public function status(OllamaService $ollamaService): JsonResponse
    {
        $ollamaAvailable = $ollamaService->testConnection();

        return response()->json([
            'success' => true,
            'data' => [
                'system' => 'QueueAI',
                'version' => '1.0.0',
                'status' => 'running',
                'ollama' => [
                    'available' => $ollamaAvailable,
                    'url' => config('ai.ollama.api_url', env('OLLAMA_API_URL')),
                ],
                'queue' => [
                    'connection' => config('queue.default'),
                    'pending_jobs' => $this->getPendingJobsCount(),
                ],
                'timestamp' => now()->toISOString(),
            ],
        ]);
    }

    /**
     * Listar modelos disponíveis
     */
    public function models(OllamaService $ollamaService): JsonResponse
    {
        $models = $ollamaService->getAvailableModels();

        return response()->json([
            'success' => true,
            'data' => $models,
        ]);
    }

    /**
     * Estatísticas do sistema
     */
    public function stats(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'prompts' => [
                    'total' => \App\Models\Prompt::count(),
                    'pending' => \App\Models\Prompt::where('status', 'pending')->count(),
                    'processing' => \App\Models\Prompt::where('status', 'processing')->count(),
                    'completed' => \App\Models\Prompt::where('status', 'completed')->count(),
                    'failed' => \App\Models\Prompt::where('status', 'failed')->count(),
                ],
                'responses' => [
                    'total' => \App\Models\Response::count(),
                    'completed' => \App\Models\Response::where('status', 'completed')->count(),
                    'failed' => \App\Models\Response::where('status', 'failed')->count(),
                ],
                'queue' => [
                    'pending_jobs' => $this->getPendingJobsCount(),
                ],
            ],
        ]);
    }

    /**
     * Obter contagem de jobs pendentes
     */
    private function getPendingJobsCount(): int
    {
        // Para SQLite/Database queue
        if (config('queue.default') === 'database') {
            return DB::table('jobs')->count();
        }

        return 0;
    }
}
