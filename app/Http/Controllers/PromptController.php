<?php

namespace App\Http\Controllers;

use App\Http\Requests\StorePromptRequest;
use App\Http\Requests\UpdatePromptRequest;
use App\Models\Prompt;
use App\Jobs\ProcessPromptJob;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PromptController extends Controller
{
    /**
     * Display a listing of the user's prompts
     */
    public function index(Request $request): JsonResponse
    {
        $prompts = Prompt::forUser($request->user()->id)
            ->with(['responses', 'subjects'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json($prompts);
    }

    /**
     * Store a new prompt
     */
    public function store(StorePromptRequest $request): JsonResponse
    {

        $prompt = Prompt::create([
            'content' => $request->content,
            'metadata' => $request->metadata ?? [],
            'user_id' => $request->user()->id,
        ]);

        // Processar arquivos anexados se existirem
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $prompt->addMediaFromRequest('attachments')
                    ->usingFileName($file->getClientOriginalName())
                    ->toMediaCollection('attachments');
            }
        }

        // Determinar quais modelos usar
        $models = $request->models; 

        // Disparar jobs para cada modelo
        foreach ($models as $model) {
            ProcessPromptJob::dispatch($prompt, $model);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'uuid' => $prompt->uuid,
                'status' => $prompt->status,
                'models' => $models,
                'created_at' => $prompt->created_at,
            ],
            'message' => 'Prompt criado e enviado para processamento.',
        ], 201);
    }

    /**
     * Show prompt details
     */
    public function show(Request $request, Prompt $prompt): JsonResponse
    {
        // Verificar se o prompt pertence ao usuário autenticado
        if ($prompt->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Prompt não encontrado.',
            ], 404);
        }

        $prompt->load(['responses', 'media']);

        return response()->json([
            'success' => true,
            'data' => [
                'uuid' => $prompt->uuid,
                'content' => $prompt->content,
                'status' => $prompt->status,
                'metadata' => json_decode($prompt->metadata),
                'attachments' => $prompt->getMedia('attachments')->map(function ($media) {
                    return [
                        'id' => $media->id,
                        'name' => $media->name,
                        'file_name' => $media->file_name,
                        'mime_type' => $media->mime_type,
                        'size' => $media->size,
                        'url' => $media->getUrl(),
                    ];
                }),
                'responses' => $prompt->responses->map(function ($response) {
                    return [
                        'uuid' => $response->uuid,
                        'model' => $response->model,
                        'content' => $response->content,
                        'status' => $response->status,
                        'metadata' => $response->metadata,
                        'generated_at' => $response->generated_at,
                    ];
                }),
                'created_at' => $prompt->created_at,
                'processed_at' => $prompt->processed_at,
            ],
        ]);
    }

    /**
     * Update prompt
     */
    public function update(UpdatePromptRequest $request, Prompt $prompt): JsonResponse
    {
        // Verificar se o prompt pertence ao usuário autenticado
        if ($prompt->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Prompt não encontrado.',
            ], 404);
        }

        // Só permitir edição se o prompt ainda não foi processado
        if ($prompt->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Não é possível editar um prompt que já foi processado.',
            ], 422);
        }

        $prompt->update($request->validated());

        return response()->json([
            'success' => true,
            'data' => [
                'uuid' => $prompt->uuid,
                'content' => $prompt->content,
                'status' => $prompt->status,
                'metadata' => $prompt->metadata,
                'updated_at' => $prompt->updated_at,
            ],
            'message' => 'Prompt atualizado com sucesso.',
        ]);
    }

    /**
     * Get prompt status
     */
    public function status(Request $request, Prompt $prompt): JsonResponse
    {
        // Verificar se o prompt pertence ao usuário autenticado
        if ($prompt->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Prompt não encontrado.',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'uuid' => $prompt->uuid,
                'status' => $prompt->status,
                'responses_count' => $prompt->responses()->count(),
                'processed_at' => $prompt->processed_at,
            ],
        ]);
    }

    /**
     * Get prompt responses
     */
    public function responses(Request $request, Prompt $prompt): JsonResponse
    {
        // Verificar se o prompt pertence ao usuário autenticado
        if ($prompt->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Prompt não encontrado.',
            ], 404);
        }

        $responses = $prompt->responses()->get();

        return response()->json([
            'success' => true,
            'data' => $responses->map(function ($response) {
                return [
                    'uuid' => $response->uuid,
                    'model' => $response->model,
                    'content' => $response->content,
                    'status' => $response->status,
                    'metadata' => $response->metadata,
                    'generated_at' => $response->generated_at,
                ];
            }),
        ]);
    }
}
